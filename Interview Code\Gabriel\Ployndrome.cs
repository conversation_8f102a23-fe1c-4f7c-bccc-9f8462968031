﻿/*Polindrome*/
// 5 точки
/*string word = Console.ReadLine();
Console.WriteLine(FindPolyndrome(word));
static bool FindPolyndrome(string word) {
    

    char[] polindrom = word.ToCharArray();
    char[] polindrom2 = new char[polindrom.Length];
    for (int i = polindrom.Length - 1; i >= 0; i--)
    {
        polindrom2[polindrom.Length - 1 - i] = polindrom[i];
    }
int counter = 0;
    for (int i = 0; i < polindrom2.Length; i++)
    {
        if (polindrom[i] == polindrom2[i])
        {
            counter++;
        }
    }
    if (counter == polindrom.Length)
    {
               return true;

    }
     
    return false;
    
}
*/

/*ReverseSentence*/
//5 точки
/*ReverseSentence();
static void ReverseSentence()
{
    string sentence = "C# is fun";
    string[] sentenceArr = sentence.Split().ToArray();

    for (int i = sentenceArr.Length - 1; i >= 0; i--)
    {
        Console.Write(sentenceArr[i] + " ");
    }
}
*/

/*Find missing number*/
//0 точки
/*int[] arr = { 1, 2, 4, 5, 6 };
int counter = 1;
for (int i = 0; i < arr.Length; i++)
{
    if (arr[i] != counter)
    {
        Console.WriteLine(i);
    }
    counter++;
}*/

/* Anagram*/

string word1 = Console.ReadLine();
string word2 = Console.ReadLine();
CheckAnagram(word1, word2);
static void CheckAnagram(string word1, string word2)
{
    char[] arr1 = word1.ToCharArray();
    char[] arr2 = word2.ToCharArray();
    arr1 = arr1.OrderBy(x=>x).ToArray();
    arr2 = arr2.OrderBy(x=>x).ToArray();
    int counter = 0;
    for (int i = 0; i < arr1.Length; i++)
    {
        if (arr1[i] == arr2[i])
        {
            counter++;
        }

    }
    if (counter == arr1.Length)
    {
        Console.WriteLine("They are anagrams");
    }
    else
    {
        Console.WriteLine("Not anagrams");
    }
}

