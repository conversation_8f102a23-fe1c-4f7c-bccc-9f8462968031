using System.IO.Ports;

namespace ModBusCom
{
    public class ModbusConfig
    {
        // TCP Configuration
        public string TcpServerIp { get; set; } = "************";
        public int TcpServerPort { get; set; } = 502;
        
        // RTU Configuration
        public string SerialPortName { get; set; } = "COM1";
        public int BaudRate { get; set; } = 9600;
        public int DataBits { get; set; } = 8;
        public Parity Parity { get; set; } = Parity.None;
        public StopBits StopBits { get; set; } = StopBits.One;
        public int ReadTimeout { get; set; } = 1000;
        public int WriteTimeout { get; set; } = 1000;
        
        // General Configuration
        public byte SlaveId { get; set; } = 1;
        public int ConnectionTimeout { get; set; } = 5000;
        
        // Register addresses for demo
        public ushort SingleRegisterAddress { get; set; } = 0;
        public ushort MultipleRegistersStartAddress { get; set; } = 10;
        public ushort FloatRegistersStartAddress { get; set; } = 20;
        public ushort Int32RegistersStartAddress { get; set; } = 30;
        public ushort SerialNumberDataStartAddress { get; set; } = 40;
        
        public static ModbusConfig Default => new ModbusConfig();
        
        public void DisplayConfiguration()
        {
            Console.WriteLine("Current Modbus Configuration:");
            Console.WriteLine($"  TCP Server: {TcpServerIp}:{TcpServerPort}");
            Console.WriteLine($"  Serial Port: {SerialPortName} ({BaudRate}, {DataBits}, {Parity}, {StopBits})");
            Console.WriteLine($"  Slave ID: {SlaveId}");
            Console.WriteLine($"  Timeouts: Read={ReadTimeout}ms, Write={WriteTimeout}ms, Connection={ConnectionTimeout}ms");
        }
    }
}
