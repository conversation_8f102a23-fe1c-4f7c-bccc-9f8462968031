using NModbus;
using NModbus.Serial;
using System.Net.Sockets;
using System.IO.Ports;
using System.Diagnostics;

namespace ModBusCom
{
    public class ModbusHelper : IDisposable
    {
        private IModbusMaster? _master;
        private TcpClient? _tcpClient;
        private SerialPort? _serialPort;
        private readonly ModbusConfig _config;
        private bool _disposed = false;

        public ModbusHelper(ModbusConfig config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
        }

        public async Task<bool> ConnectTcpAsync()
        {
            try
            {
                _tcpClient = new TcpClient();
                await _tcpClient.ConnectAsync(_config.TcpServerIp, _config.TcpServerPort);
                
                var factory = new ModbusFactory();
                _master = factory.CreateMaster(_tcpClient);
                
                Console.WriteLine($"Connected to TCP Modbus server at {_config.TcpServerIp}:{_config.TcpServerPort}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to connect to TCP Modbus server: {ex.Message}");
                return false;
            }
        }

        public bool ConnectRtu()
        {
            try
            {
                _serialPort = new SerialPort(_config.SerialPortName, _config.BaudRate, _config.Parity, _config.DataBits, _config.StopBits)
                {
                    ReadTimeout = _config.ReadTimeout,
                    WriteTimeout = _config.WriteTimeout
                };
                
                _serialPort.Open();
                
                var factory = new ModbusFactory();
                _master = factory.CreateRtuMaster(_serialPort);
                
                Console.WriteLine($"Connected to RTU Modbus device on {_config.SerialPortName}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to connect to RTU Modbus device: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            if (_master == null)
                return false;

            try
            {
                await _master.ReadHoldingRegistersAsync(_config.SlaveId, 0, 1);
                return true;
            }
            catch
            {
                return false;
            }
        }

        // Write operations
        public async Task WriteSingleRegisterAsync(ushort address, ushort value)
        {
            if (_master == null) throw new InvalidOperationException("Not connected to Modbus device");
            
            await _master.WriteSingleRegisterAsync(_config.SlaveId, address, value);
            Console.WriteLine($"Written value {value} to register {address}");
        }

        public async Task WriteMultipleRegistersAsync(ushort startAddress, ushort[] values)
        {
            if (_master == null) throw new InvalidOperationException("Not connected to Modbus device");
           
            var sw = new Stopwatch();
            sw.Start();             
            await _master.WriteMultipleRegistersAsync(_config.SlaveId, startAddress, values);
            sw.Stop();
            Console.WriteLine($"Time taken: {sw.ElapsedMilliseconds} ms");
            Console.WriteLine($"Written {values.Length} values to registers starting at {startAddress}");
        }

        public async Task WriteFloatAsync(ushort startAddress, float value)
        {
            ushort[] registers = ConvertFloatToRegisters(value);
            await WriteMultipleRegistersAsync(startAddress, registers);
            Console.WriteLine($"Written float value {value} to registers {startAddress}-{startAddress + 1}");
        }

        public async Task WriteInt32Async(ushort startAddress, int value)
        {
            ushort[] registers = ConvertInt32ToRegisters(value);
            await WriteMultipleRegistersAsync(startAddress, registers);
            Console.WriteLine($"Written int32 value {value} to registers {startAddress}-{startAddress + 1}");
        }

        // Read operations
        public async Task<ushort> ReadSingleRegisterAsync(ushort address)
        {
            if (_master == null) throw new InvalidOperationException("Not connected to Modbus device");
            
            ushort[] result = await _master.ReadHoldingRegistersAsync(_config.SlaveId, address, 1);
            return result[0];
        }

        public async Task<ushort[]> ReadMultipleRegistersAsync(ushort startAddress, ushort numberOfRegisters)
        {
            if (_master == null) throw new InvalidOperationException("Not connected to Modbus device");
            
            return await _master.ReadHoldingRegistersAsync(_config.SlaveId, startAddress, numberOfRegisters);
        }

        public async Task<float> ReadFloatAsync(ushort startAddress)
        {
            ushort[] registers = await ReadMultipleRegistersAsync(startAddress, 2);
            return ConvertRegistersToFloat(registers);
        }

        public async Task<int> ReadInt32Async(ushort startAddress)
        {
            ushort[] registers = await ReadMultipleRegistersAsync(startAddress, 2);
            return ConvertRegistersToInt32(registers);
        }

        // Serial Number Data operations
        public async Task WriteSerialNumberDataAsync(ushort startAddress, SerialNumberData serialData)
        {
            
            ushort[] registers = serialData.ToModbusRegisters();
            await WriteMultipleRegistersAsync(startAddress, registers);
            Console.WriteLine($"Written serial number data to registers starting at {startAddress}: {serialData}");
            
       }

        public async Task<SerialNumberData> ReadSerialNumberDataAsync(ushort startAddress)
        {
            ushort[] registers = await ReadMultipleRegistersAsync(startAddress, 12); // 24 bytes = 12 registers
            return SerialNumberData.FromModbusRegisters(registers);
        }

        // Utility methods
        private static ushort[] ConvertFloatToRegisters(float value)
        {
            byte[] bytes = BitConverter.GetBytes(value);
            return new ushort[]
            {
                (ushort)((bytes[3] << 8) | bytes[2]), // High word
                (ushort)((bytes[1] << 8) | bytes[0])  // Low word
            };
        }

        private static float ConvertRegistersToFloat(ushort[] registers)
        {
            if (registers.Length < 2)
                throw new ArgumentException("At least 2 registers required for float conversion");
            
            byte[] bytes = new byte[4];
            bytes[3] = (byte)(registers[0] >> 8);
            bytes[2] = (byte)(registers[0] & 0xFF);
            bytes[1] = (byte)(registers[1] >> 8);
            bytes[0] = (byte)(registers[1] & 0xFF);
            
            return BitConverter.ToSingle(bytes, 0);
        }

        private static ushort[] ConvertInt32ToRegisters(int value)
        {
            return new ushort[]
            {
                (ushort)(value >> 16),        // High word
                (ushort)(value & 0xFFFF)      // Low word
            };
        }

        private static int ConvertRegistersToInt32(ushort[] registers)
        {
            if (registers.Length < 2)
                throw new ArgumentException("At least 2 registers required for int32 conversion");
            
            return (registers[0] << 16) | registers[1];
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _master?.Dispose();
                _tcpClient?.Close();
                _serialPort?.Close();
                _disposed = true;
            }
        }
    }
}
