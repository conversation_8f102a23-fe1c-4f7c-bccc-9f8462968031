using NModbus;
using System.Net.Sockets;
using System.IO.Ports;
using System.Text;
using System.Linq;

namespace ModBusCom
{
    /// <summary>
    /// Structure for serial number data containing product information and test results
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Modbus Communication Demo");
            Console.WriteLine("========================");

            var config = ModbusConfig.Default;
            config.DisplayConfiguration();

            while (true)
            {
                Console.WriteLine("\nSelect communication type:");
                Console.WriteLine("1. TCP Modbus");
                Console.WriteLine("2. RTU Modbus (Serial)");
                Console.WriteLine("3. Interactive Mode");
                Console.WriteLine("4. Exit");
                Console.Write("Enter your choice (1-4): ");

                string? choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        await DemoTcpModbus(config);
                        break;
                    case "2":
                        DemoRtuModbus(config);
                        break;
                    case "3":
                        await InteractiveMode(config);
                        break;
                    case "4":
                        Console.WriteLine("Goodbye!");
                        return;
                    default:
                        Console.WriteLine("Invalid choice. Please try again.");
                        break;
                }

                Console.WriteLine("\nPress any key to continue...");
                Console.ReadKey();
            }
        }

        static async Task DemoTcpModbus(ModbusConfig config)
        {
            Console.WriteLine("\n--- TCP Modbus Demo ---");

            using var helper = new ModbusHelper(config);

            if (await helper.ConnectTcpAsync())
            {
                if (await helper.TestConnectionAsync())
                {
                    Console.WriteLine("Connection test successful!");

                    // Demo: Write holding registers
                    await WriteHoldingRegistersDemo(helper, config);

                    // Demo: Read holding registers
                    await ReadHoldingRegistersDemo(helper, config);

                    Console.WriteLine("TCP Modbus demo completed successfully!");
                }
                else
                {
                    Console.WriteLine("Connection test failed. Device may not be responding.");
                }
            }
        }

        static void DemoRtuModbus(ModbusConfig config)
        {
            Console.WriteLine("\n--- RTU Modbus Demo ---");

            using var helper = new ModbusHelper(config);

            if (helper.ConnectRtu())
            {
                if (helper.TestConnectionAsync().Result)
                {
                    Console.WriteLine("Connection test successful!");

                    // Demo: Write holding registers
                    WriteHoldingRegistersDemo(helper, config).Wait();

                    // Demo: Read holding registers
                    ReadHoldingRegistersDemo(helper, config).Wait();

                    Console.WriteLine("RTU Modbus demo completed successfully!");
                }
                else
                {
                    Console.WriteLine("Connection test failed. Device may not be responding.");
                }
            }
        }

        static async Task InteractiveMode(ModbusConfig config)
        {
            Console.WriteLine("\n--- Interactive Modbus Mode ---");
            Console.WriteLine("Choose connection type:");
            Console.WriteLine("1. TCP");
            Console.WriteLine("2. RTU");
            Console.Write("Enter choice (1-2): ");

            string? connectionChoice = Console.ReadLine();

            using var helper = new ModbusHelper(config);
            bool connected = false;

            if (connectionChoice == "1")
            {
                connected = await helper.ConnectTcpAsync();
            }
            else if (connectionChoice == "2")
            {
                connected = helper.ConnectRtu();
            }
            else
            {
                Console.WriteLine("Invalid choice.");
                return;
            }

            if (!connected)
            {
                Console.WriteLine("Failed to connect to Modbus device.");
                return;
            }

            if (!await helper.TestConnectionAsync())
            {
                Console.WriteLine("Connection test failed. Device may not be responding.");
                return;
            }

            Console.WriteLine("Connected successfully! Enter commands (type 'help' for available commands):");

            while (true)
            {
                Console.Write("> ");
                string? input = Console.ReadLine();

                if (string.IsNullOrWhiteSpace(input))
                    continue;

                string[] parts = input.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                string command = parts[0].ToLower();

                try
                {
                    switch (command)
                    {
                        case "help":
                            ShowInteractiveHelp();
                            break;
                        case "read":
                            await HandleReadCommand(helper, parts);
                            break;
                        case "write":
                            await HandleWriteCommand(helper, parts);
                            break;
                        case "readfloat":
                            await HandleReadFloatCommand(helper, parts);
                            break;
                        case "writefloat":
                            await HandleWriteFloatCommand(helper, parts);
                            break;
                        case "readint32":
                            await HandleReadInt32Command(helper, parts);
                            break;
                        case "writeint32":
                            await HandleWriteInt32Command(helper, parts);
                            break;
                        case "readserial":
                            await HandleReadSerialCommand(helper, parts);
                            break;
                        case "writeserial":
                            await HandleWriteSerialCommand(helper, parts);
                            break;
                        case "randomserial":
                            await HandleRandomSerialCommand(helper, parts);
                            break;
                        case "exit":
                        case "quit":
                            return;
                        default:
                            Console.WriteLine("Unknown command. Type 'help' for available commands.");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                }
            }
        }

        static void ShowInteractiveHelp()
        {
            Console.WriteLine("Available commands:");
            Console.WriteLine("  read <address> [count]        - Read holding register(s)");
            Console.WriteLine("  write <address> <value>       - Write single holding register");
            Console.WriteLine("  readfloat <address>           - Read float from 2 registers");
            Console.WriteLine("  writefloat <address> <value>  - Write float to 2 registers");
            Console.WriteLine("  readint32 <address>           - Read int32 from 2 registers");
            Console.WriteLine("  writeint32 <address> <value>  - Write int32 to 2 registers");
            Console.WriteLine("  readserial <address>          - Read serial number data from 12 registers");
            Console.WriteLine("  writeserial <address> <serial> <grade> <status> <reason> - Write serial number data");
            Console.WriteLine("  randomserial <address> [count] - Write random serial number data (count defaults to 1)");
            Console.WriteLine("  help                          - Show this help");
            Console.WriteLine("  exit/quit                     - Exit interactive mode");
        }

        static async Task HandleReadCommand(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 2)
            {
                Console.WriteLine("Usage: read <address> [count]");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            ushort count = 1;
            if (parts.Length > 2 && !ushort.TryParse(parts[2], out count))
            {
                Console.WriteLine("Invalid count format");
                return;
            }

            if (count == 1)
            {
                ushort value = await helper.ReadSingleRegisterAsync(address);
                Console.WriteLine($"Register {address}: {value} (0x{value:X4})");
            }
            else
            {
                ushort[] values = await helper.ReadMultipleRegistersAsync(address, count);
                Console.WriteLine($"Registers {address}-{address + count - 1}:");
                for (int i = 0; i < values.Length; i++)
                {
                    Console.WriteLine($"  {address + i}: {values[i]} (0x{values[i]:X4})");
                }
            }
        }

        static async Task HandleWriteCommand(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 3)
            {
                Console.WriteLine("Usage: write <address> <value>");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            if (!ushort.TryParse(parts[2], out ushort value))
            {
                Console.WriteLine("Invalid value format");
                return;
            }

            await helper.WriteSingleRegisterAsync(address, value);
        }

        static async Task HandleReadFloatCommand(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 2)
            {
                Console.WriteLine("Usage: readfloat <address>");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            float value = await helper.ReadFloatAsync(address);
            Console.WriteLine($"Float at registers {address}-{address + 1}: {value}");
        }

        static async Task HandleWriteFloatCommand(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 3)
            {
                Console.WriteLine("Usage: writefloat <address> <value>");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            if (!float.TryParse(parts[2], out float value))
            {
                Console.WriteLine("Invalid value format");
                return;
            }

            await helper.WriteFloatAsync(address, value);
        }

        static async Task HandleReadInt32Command(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 2)
            {
                Console.WriteLine("Usage: readint32 <address>");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            int value = await helper.ReadInt32Async(address);
            Console.WriteLine($"Int32 at registers {address}-{address + 1}: {value}");
        }

        static async Task HandleWriteInt32Command(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 3)
            {
                Console.WriteLine("Usage: writeint32 <address> <value>");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            if (!int.TryParse(parts[2], out int value))
            {
                Console.WriteLine("Invalid value format");
                return;
            }

            await helper.WriteInt32Async(address, value);
        }

        static async Task HandleReadSerialCommand(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 2)
            {
                Console.WriteLine("Usage: readserial <address>");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            SerialNumberData serialData = await helper.ReadSerialNumberDataAsync(address);
            Console.WriteLine($"Serial Number Data at registers {address}-{address + 11}:");
            Console.WriteLine($"  Serial Number: '{serialData.SerialNumber.Trim()}'");
            Console.WriteLine($"  Grade: '{serialData.Grade}'");
            Console.WriteLine($"  Status: '{serialData.Status}' ({(serialData.IsAccepted ? "Accepted" : serialData.IsRejected ? "Rejected" : "Unknown")})");
            Console.WriteLine($"  Reason Code: {serialData.ReasonCode}");
        }

        static async Task HandleWriteSerialCommand(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 6)
            {
                Console.WriteLine("Usage: writeserial <address> <serial> <grade> <status> <reason>");
                Console.WriteLine("  <serial>: Serial number (up to 20 characters)");
                Console.WriteLine("  <grade>: Single character grade (A, B, C, etc.)");
                Console.WriteLine("  <status>: 1 for Accepted, 2 for Rejected");
                Console.WriteLine("  <reason>: Reason code (0-65535)");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            string serialNumber = parts[2];
            if (serialNumber.Length > 20)
            {
                Console.WriteLine("Serial number too long (max 20 characters)");
                return;
            }

            if (parts[3].Length != 1)
            {
                Console.WriteLine("Grade must be a single character");
                return;
            }
            char grade = parts[3][0];

            if (parts[4].Length != 1)
            {
                Console.WriteLine("Status must be a single character (A or R)");
                return;
            }
            char status = parts[4][0];

            if (!ushort.TryParse(parts[5], out ushort reasonCode))
            {
                Console.WriteLine("Invalid reason code format (must be 0-65535)");
                return;
            }

            SerialNumberData serialData = new SerialNumberData(serialNumber, grade, status, reasonCode);
            await helper.WriteSerialNumberDataAsync(address, serialData);
        }

        static async Task HandleRandomSerialCommand(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 2)
            {
                Console.WriteLine("Usage: randomserial <address> [count]");
                Console.WriteLine("  <address>: Starting register address");
                Console.WriteLine("  [count]: Number of random serial entries to generate (default: 1)");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            int count = 1;
            if (parts.Length > 2 && !int.TryParse(parts[2], out count))
            {
                Console.WriteLine("Invalid count format");
                return;
            }

            if (count < 1 || count > 10)
            {
                Console.WriteLine("Count must be between 1 and 10");
                return;
            }

            Random random = new Random();
            Console.WriteLine($"Generating {count} random serial number data entries starting at address {address}:");

            for (int i = 0; i < count; i++)
            {
                ushort currentAddress = (ushort)(address + (i * 12)); // Each entry uses 12 registers
                SerialNumberData randomData = SerialNumberData.GenerateRandom(random);

                Console.WriteLine($"  Entry {i + 1} at address {currentAddress}: {randomData}");
                await helper.WriteSerialNumberDataAsync(currentAddress, randomData);
            }

            Console.WriteLine($"Successfully wrote {count} random serial number data entries!");
        }

        static async Task WriteHoldingRegistersDemo(ModbusHelper helper, ModbusConfig config)
        {
            Console.WriteLine("\n--- Writing Holding Registers ---");

            try
            {
                // Example 1: Write single holding register
                ushort singleValue = 1234;
                Console.WriteLine($"Writing single register at address {config.SingleRegisterAddress} with value {singleValue}");
                await helper.WriteSingleRegisterAsync(config.SingleRegisterAddress, singleValue);
                Console.WriteLine("Single register write successful!");

                // Example 2: Write multiple holding registers
                ushort[] multipleValues = { 100, 200, 300, 400, 500 };
                Console.WriteLine($"Writing {multipleValues.Length} registers starting at address {config.MultipleRegistersStartAddress}");
                Console.WriteLine($"Values: [{string.Join(", ", multipleValues)}]");
                await helper.WriteMultipleRegistersAsync(config.MultipleRegistersStartAddress, multipleValues);
                Console.WriteLine("Multiple registers write successful!");

                // Example 3: Write floating point value (using 2 registers)
                float floatValue = 123.45f;
                Console.WriteLine($"Writing float value {floatValue} to registers {config.FloatRegistersStartAddress}-{config.FloatRegistersStartAddress + 1}");
                await helper.WriteFloatAsync(config.FloatRegistersStartAddress, floatValue);
                Console.WriteLine("Float value write successful!");

                // Example 4: Write 32-bit integer value (using 2 registers)
                int int32Value = 987654321;
                Console.WriteLine($"Writing int32 value {int32Value} to registers {config.Int32RegistersStartAddress}-{config.Int32RegistersStartAddress + 1}");
                await helper.WriteInt32Async(config.Int32RegistersStartAddress, int32Value);
                Console.WriteLine("Int32 value write successful!");

                // Example 5: Write serial number data (using 12 registers)
                Console.WriteLine("\n--- Random Serial Number Data Demo ---");
                SerialNumberData[] randomSerialBatch = SerialNumberData.GenerateRandomBatch(3);

                for (int i = 0; i < randomSerialBatch.Length; i++)
                {
                    ushort serialAddress = (ushort)(config.SerialNumberDataStartAddress + (i * 12));
                    Console.WriteLine($"Writing random serial #{i + 1} to registers {serialAddress}-{serialAddress + 11}");
                    Console.WriteLine($"  Data: {randomSerialBatch[i]}");
                    await helper.WriteSerialNumberDataAsync(serialAddress, randomSerialBatch[i]);
                }
                Console.WriteLine("Random serial number data batch write successful!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error writing holding registers: {ex.Message}");
            }
        }

        static async Task ReadHoldingRegistersDemo(ModbusHelper helper, ModbusConfig config)
        {
            Console.WriteLine("\n--- Reading Holding Registers ---");

            try
            {
                // Example 1: Read single holding register
                Console.WriteLine($"Reading single register at address {config.SingleRegisterAddress}");
                ushort singleValue = await helper.ReadSingleRegisterAsync(config.SingleRegisterAddress);
                Console.WriteLine($"Register {config.SingleRegisterAddress} value: {singleValue}");

                // Example 2: Read multiple holding registers
                ushort numberOfRegisters = 5;
                Console.WriteLine($"Reading {numberOfRegisters} registers starting at address {config.MultipleRegistersStartAddress}");
                ushort[] multipleResults = await helper.ReadMultipleRegistersAsync(config.MultipleRegistersStartAddress, numberOfRegisters);

                Console.WriteLine("Register values:");
                for (int i = 0; i < multipleResults.Length; i++)
                {
                    Console.WriteLine($"  Register {config.MultipleRegistersStartAddress + i}: {multipleResults[i]} (0x{multipleResults[i]:X4})");
                }

                // Example 3: Read floating point value (from 2 registers)
                Console.WriteLine($"Reading float value from registers {config.FloatRegistersStartAddress}-{config.FloatRegistersStartAddress + 1}");
                float floatValue = await helper.ReadFloatAsync(config.FloatRegistersStartAddress);
                Console.WriteLine($"Float value: {floatValue}");

                // Example 4: Read 32-bit integer value (from 2 registers)
                Console.WriteLine($"Reading int32 value from registers {config.Int32RegistersStartAddress}-{config.Int32RegistersStartAddress + 1}");
                int int32Value = await helper.ReadInt32Async(config.Int32RegistersStartAddress);
                Console.WriteLine($"Int32 value: {int32Value}");

                // Example 5: Read serial number data (from 12 registers each)
                Console.WriteLine("\n--- Reading Random Serial Number Data ---");
                for (int i = 0; i < 3; i++)
                {
                    ushort serialAddress = (ushort)(config.SerialNumberDataStartAddress + (i * 12));
                    Console.WriteLine($"Reading serial #{i + 1} from registers {serialAddress}-{serialAddress + 11}");
                    SerialNumberData readSerialData = await helper.ReadSerialNumberDataAsync(serialAddress);
                    Console.WriteLine($"  Data: {readSerialData}");
                    Console.WriteLine($"  Details:");
                    Console.WriteLine($"    Serial Number: '{readSerialData.SerialNumber.Trim()}'");
                    Console.WriteLine($"    Grade: '{readSerialData.Grade}'");
                    Console.WriteLine($"    Status: '{readSerialData.Status}' ({(readSerialData.IsAccepted ? "Accepted" : readSerialData.IsRejected ? "Rejected" : "Unknown")})");
                    Console.WriteLine($"    Reason Code: {readSerialData.ReasonCode}");
                }

                // Example 5: Read and display register values in different formats
                Console.WriteLine("\n--- Register Value Formats ---");
                ushort testValue = await helper.ReadSingleRegisterAsync(config.SingleRegisterAddress);

                Console.WriteLine($"Register {config.SingleRegisterAddress}:");
                Console.WriteLine($"  Decimal: {testValue}");
                Console.WriteLine($"  Hexadecimal: 0x{testValue:X4}");
                Console.WriteLine($"  Binary: {Convert.ToString(testValue, 2).PadLeft(16, '0')}");
                Console.WriteLine($"  Signed: {(short)testValue}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading holding registers: {ex.Message}");
            }
        }


    }
}
