using NModbus;
using System.Net.Sockets;
using System.IO.Ports;

namespace ModBusCom
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Modbus Communication Demo");
            Console.WriteLine("========================");

            var config = ModbusConfig.Default;
            config.DisplayConfiguration();

            while (true)
            {
                Console.WriteLine("\nSelect communication type:");
                Console.WriteLine("1. TCP Modbus");
                Console.WriteLine("2. RTU Modbus (Serial)");
                Console.WriteLine("3. Interactive Mode");
                Console.WriteLine("4. Exit");
                Console.Write("Enter your choice (1-4): ");

                string? choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        await DemoTcpModbus(config);
                        break;
                    case "2":
                        DemoRtuModbus(config);
                        break;
                    case "3":
                        await InteractiveMode(config);
                        break;
                    case "4":
                        Console.WriteLine("Goodbye!");
                        return;
                    default:
                        Console.WriteLine("Invalid choice. Please try again.");
                        break;
                }

                Console.WriteLine("\nPress any key to continue...");
                Console.ReadKey();
            }
        }

        static async Task DemoTcpModbus(ModbusConfig config)
        {
            Console.WriteLine("\n--- TCP Modbus Demo ---");

            using var helper = new ModbusHelper(config);

            if (await helper.ConnectTcpAsync())
            {
                if (await helper.TestConnectionAsync())
                {
                    Console.WriteLine("Connection test successful!");

                    // Demo: Write holding registers
                    await WriteHoldingRegistersDemo(helper, config);

                    // Demo: Read holding registers
                    await ReadHoldingRegistersDemo(helper, config);

                    Console.WriteLine("TCP Modbus demo completed successfully!");
                }
                else
                {
                    Console.WriteLine("Connection test failed. Device may not be responding.");
                }
            }
        }

        static void DemoRtuModbus(ModbusConfig config)
        {
            Console.WriteLine("\n--- RTU Modbus Demo ---");

            using var helper = new ModbusHelper(config);

            if (helper.ConnectRtu())
            {
                if (helper.TestConnectionAsync().Result)
                {
                    Console.WriteLine("Connection test successful!");

                    // Demo: Write holding registers
                    WriteHoldingRegistersDemo(helper, config).Wait();

                    // Demo: Read holding registers
                    ReadHoldingRegistersDemo(helper, config).Wait();

                    Console.WriteLine("RTU Modbus demo completed successfully!");
                }
                else
                {
                    Console.WriteLine("Connection test failed. Device may not be responding.");
                }
            }
        }

        static async Task InteractiveMode(ModbusConfig config)
        {
            Console.WriteLine("\n--- Interactive Modbus Mode ---");
            Console.WriteLine("Choose connection type:");
            Console.WriteLine("1. TCP");
            Console.WriteLine("2. RTU");
            Console.Write("Enter choice (1-2): ");

            string? connectionChoice = Console.ReadLine();

            using var helper = new ModbusHelper(config);
            bool connected = false;

            if (connectionChoice == "1")
            {
                connected = await helper.ConnectTcpAsync();
            }
            else if (connectionChoice == "2")
            {
                connected = helper.ConnectRtu();
            }
            else
            {
                Console.WriteLine("Invalid choice.");
                return;
            }

            if (!connected)
            {
                Console.WriteLine("Failed to connect to Modbus device.");
                return;
            }

            if (!await helper.TestConnectionAsync())
            {
                Console.WriteLine("Connection test failed. Device may not be responding.");
                return;
            }

            Console.WriteLine("Connected successfully! Enter commands (type 'help' for available commands):");

            while (true)
            {
                Console.Write("> ");
                string? input = Console.ReadLine();

                if (string.IsNullOrWhiteSpace(input))
                    continue;

                string[] parts = input.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                string command = parts[0].ToLower();

                try
                {
                    switch (command)
                    {
                        case "help":
                            ShowInteractiveHelp();
                            break;
                        case "read":
                            await HandleReadCommand(helper, parts);
                            break;
                        case "write":
                            await HandleWriteCommand(helper, parts);
                            break;
                        case "readfloat":
                            await HandleReadFloatCommand(helper, parts);
                            break;
                        case "writefloat":
                            await HandleWriteFloatCommand(helper, parts);
                            break;
                        case "readint32":
                            await HandleReadInt32Command(helper, parts);
                            break;
                        case "writeint32":
                            await HandleWriteInt32Command(helper, parts);
                            break;
                        case "exit":
                        case "quit":
                            return;
                        default:
                            Console.WriteLine("Unknown command. Type 'help' for available commands.");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                }
            }
        }

        static void ShowInteractiveHelp()
        {
            Console.WriteLine("Available commands:");
            Console.WriteLine("  read <address> [count]        - Read holding register(s)");
            Console.WriteLine("  write <address> <value>       - Write single holding register");
            Console.WriteLine("  readfloat <address>           - Read float from 2 registers");
            Console.WriteLine("  writefloat <address> <value>  - Write float to 2 registers");
            Console.WriteLine("  readint32 <address>           - Read int32 from 2 registers");
            Console.WriteLine("  writeint32 <address> <value>  - Write int32 to 2 registers");
            Console.WriteLine("  help                          - Show this help");
            Console.WriteLine("  exit/quit                     - Exit interactive mode");
        }

        static async Task HandleReadCommand(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 2)
            {
                Console.WriteLine("Usage: read <address> [count]");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            ushort count = 1;
            if (parts.Length > 2 && !ushort.TryParse(parts[2], out count))
            {
                Console.WriteLine("Invalid count format");
                return;
            }

            if (count == 1)
            {
                ushort value = await helper.ReadSingleRegisterAsync(address);
                Console.WriteLine($"Register {address}: {value} (0x{value:X4})");
            }
            else
            {
                ushort[] values = await helper.ReadMultipleRegistersAsync(address, count);
                Console.WriteLine($"Registers {address}-{address + count - 1}:");
                for (int i = 0; i < values.Length; i++)
                {
                    Console.WriteLine($"  {address + i}: {values[i]} (0x{values[i]:X4})");
                }
            }
        }

        static async Task HandleWriteCommand(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 3)
            {
                Console.WriteLine("Usage: write <address> <value>");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            if (!ushort.TryParse(parts[2], out ushort value))
            {
                Console.WriteLine("Invalid value format");
                return;
            }

            await helper.WriteSingleRegisterAsync(address, value);
        }

        static async Task HandleReadFloatCommand(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 2)
            {
                Console.WriteLine("Usage: readfloat <address>");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            float value = await helper.ReadFloatAsync(address);
            Console.WriteLine($"Float at registers {address}-{address + 1}: {value}");
        }

        static async Task HandleWriteFloatCommand(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 3)
            {
                Console.WriteLine("Usage: writefloat <address> <value>");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            if (!float.TryParse(parts[2], out float value))
            {
                Console.WriteLine("Invalid value format");
                return;
            }

            await helper.WriteFloatAsync(address, value);
        }

        static async Task HandleReadInt32Command(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 2)
            {
                Console.WriteLine("Usage: readint32 <address>");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            int value = await helper.ReadInt32Async(address);
            Console.WriteLine($"Int32 at registers {address}-{address + 1}: {value}");
        }

        static async Task HandleWriteInt32Command(ModbusHelper helper, string[] parts)
        {
            if (parts.Length < 3)
            {
                Console.WriteLine("Usage: writeint32 <address> <value>");
                return;
            }

            if (!ushort.TryParse(parts[1], out ushort address))
            {
                Console.WriteLine("Invalid address format");
                return;
            }

            if (!int.TryParse(parts[2], out int value))
            {
                Console.WriteLine("Invalid value format");
                return;
            }

            await helper.WriteInt32Async(address, value);
        }

        static async Task WriteHoldingRegistersDemo(ModbusHelper helper, ModbusConfig config)
        {
            Console.WriteLine("\n--- Writing Holding Registers ---");

            try
            {
                // Example 1: Write single holding register
                ushort singleValue = 1234;
                Console.WriteLine($"Writing single register at address {config.SingleRegisterAddress} with value {singleValue}");
                await helper.WriteSingleRegisterAsync(config.SingleRegisterAddress, singleValue);
                Console.WriteLine("Single register write successful!");

                // Example 2: Write multiple holding registers
                ushort[] multipleValues = { 100, 200, 300, 400, 500 };
                Console.WriteLine($"Writing {multipleValues.Length} registers starting at address {config.MultipleRegistersStartAddress}");
                Console.WriteLine($"Values: [{string.Join(", ", multipleValues)}]");
                await helper.WriteMultipleRegistersAsync(config.MultipleRegistersStartAddress, multipleValues);
                Console.WriteLine("Multiple registers write successful!");

                // Example 3: Write floating point value (using 2 registers)
                float floatValue = 123.45f;
                Console.WriteLine($"Writing float value {floatValue} to registers {config.FloatRegistersStartAddress}-{config.FloatRegistersStartAddress + 1}");
                await helper.WriteFloatAsync(config.FloatRegistersStartAddress, floatValue);
                Console.WriteLine("Float value write successful!");

                // Example 4: Write 32-bit integer value (using 2 registers)
                int int32Value = 987654321;
                Console.WriteLine($"Writing int32 value {int32Value} to registers {config.Int32RegistersStartAddress}-{config.Int32RegistersStartAddress + 1}");
                await helper.WriteInt32Async(config.Int32RegistersStartAddress, int32Value);
                Console.WriteLine("Int32 value write successful!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error writing holding registers: {ex.Message}");
            }
        }

        static async Task ReadHoldingRegistersDemo(ModbusHelper helper, ModbusConfig config)
        {
            Console.WriteLine("\n--- Reading Holding Registers ---");

            try
            {
                // Example 1: Read single holding register
                Console.WriteLine($"Reading single register at address {config.SingleRegisterAddress}");
                ushort singleValue = await helper.ReadSingleRegisterAsync(config.SingleRegisterAddress);
                Console.WriteLine($"Register {config.SingleRegisterAddress} value: {singleValue}");

                // Example 2: Read multiple holding registers
                ushort numberOfRegisters = 5;
                Console.WriteLine($"Reading {numberOfRegisters} registers starting at address {config.MultipleRegistersStartAddress}");
                ushort[] multipleResults = await helper.ReadMultipleRegistersAsync(config.MultipleRegistersStartAddress, numberOfRegisters);

                Console.WriteLine("Register values:");
                for (int i = 0; i < multipleResults.Length; i++)
                {
                    Console.WriteLine($"  Register {config.MultipleRegistersStartAddress + i}: {multipleResults[i]} (0x{multipleResults[i]:X4})");
                }

                // Example 3: Read floating point value (from 2 registers)
                Console.WriteLine($"Reading float value from registers {config.FloatRegistersStartAddress}-{config.FloatRegistersStartAddress + 1}");
                float floatValue = await helper.ReadFloatAsync(config.FloatRegistersStartAddress);
                Console.WriteLine($"Float value: {floatValue}");

                // Example 4: Read 32-bit integer value (from 2 registers)
                Console.WriteLine($"Reading int32 value from registers {config.Int32RegistersStartAddress}-{config.Int32RegistersStartAddress + 1}");
                int int32Value = await helper.ReadInt32Async(config.Int32RegistersStartAddress);
                Console.WriteLine($"Int32 value: {int32Value}");

                // Example 5: Read and display register values in different formats
                Console.WriteLine("\n--- Register Value Formats ---");
                ushort testValue = await helper.ReadSingleRegisterAsync(config.SingleRegisterAddress);

                Console.WriteLine($"Register {config.SingleRegisterAddress}:");
                Console.WriteLine($"  Decimal: {testValue}");
                Console.WriteLine($"  Hexadecimal: 0x{testValue:X4}");
                Console.WriteLine($"  Binary: {Convert.ToString(testValue, 2).PadLeft(16, '0')}");
                Console.WriteLine($"  Signed: {(short)testValue}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading holding registers: {ex.Message}");
            }
        }


    }
}
