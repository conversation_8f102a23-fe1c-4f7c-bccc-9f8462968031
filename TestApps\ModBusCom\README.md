# Modbus Communication Application

This application demonstrates how to read and write Modbus holding registers using both TCP and RTU (Serial) communication protocols.

## Features

- **TCP Modbus Communication**: Connect to Modbus TCP servers over Ethernet
- **RTU Modbus Communication**: Connect to Modbus RTU devices over serial ports
- **Holding Register Operations**: Read and write single or multiple holding registers
- **Data Type Support**: Handle different data types including:
  - 16-bit unsigned integers (single register)
  - 32-bit signed integers (two registers)
  - IEEE 754 floating-point numbers (two registers)
- **Interactive Mode**: Command-line interface for real-time Modbus operations
- **Configurable Settings**: Easy configuration of connection parameters

## Dependencies

- .NET 8.0
- NModbus library (version 3.0.81)

## Configuration

The application uses the `ModbusConfig` class to configure connection settings:

### TCP Configuration
- **Server IP**: Default is `127.0.0.1` (localhost)
- **Server Port**: Default is `502` (standard Modbus TCP port)

### RTU Configuration
- **Serial Port**: Default is `COM1`
- **Baud Rate**: Default is `9600`
- **Data Bits**: Default is `8`
- **Parity**: Default is `None`
- **Stop Bits**: Default is `One`
- **Timeouts**: Read/Write timeout default is `1000ms`

### Register Addresses
- **Single Register**: Address `0`
- **Multiple Registers**: Starting at address `10`
- **Float Registers**: Starting at address `20`
- **Int32 Registers**: Starting at address `30`

## Usage

### Running the Application

1. Build and run the application:
   ```bash
   dotnet run
   ```

2. Choose from the main menu:
   - **1**: TCP Modbus Demo
   - **2**: RTU Modbus Demo
   - **3**: Interactive Mode
   - **4**: Exit

### Demo Modes

#### TCP/RTU Demo
Both demo modes perform the following operations:
1. Connect to the Modbus device
2. Test the connection
3. Write various data types to holding registers
4. Read back the written values
5. Display results in different formats

#### Interactive Mode
Interactive mode provides a command-line interface for real-time operations:

**Available Commands:**
- `read <address> [count]` - Read holding register(s)
- `write <address> <value>` - Write single holding register
- `readfloat <address>` - Read float from 2 registers
- `writefloat <address> <value>` - Write float to 2 registers
- `readint32 <address>` - Read int32 from 2 registers
- `writeint32 <address> <value>` - Write int32 to 2 registers
- `help` - Show available commands
- `exit` or `quit` - Exit interactive mode

**Example Interactive Session:**
```
> write 0 1234
Written value 1234 to register 0
> read 0
Register 0: 1234 (0x04D2)
> writefloat 20 123.45
Written float value 123.45 to registers 20-21
> readfloat 20
Float at registers 20-21: 123.45
```

## Code Structure

### Main Classes

1. **Program.cs**: Main application entry point and demo methods
2. **ModbusHelper.cs**: Wrapper class for Modbus operations
3. **ModbusConfig.cs**: Configuration settings

### Key Methods

#### ModbusHelper Class
- `ConnectTcpAsync()`: Connect to TCP Modbus server
- `ConnectRtu()`: Connect to RTU Modbus device
- `WriteSingleRegisterAsync()`: Write single holding register
- `WriteMultipleRegistersAsync()`: Write multiple holding registers
- `WriteFloatAsync()`: Write float value (2 registers)
- `WriteInt32Async()`: Write 32-bit integer (2 registers)
- `ReadSingleRegisterAsync()`: Read single holding register
- `ReadMultipleRegistersAsync()`: Read multiple holding registers
- `ReadFloatAsync()`: Read float value (2 registers)
- `ReadInt32Async()`: Read 32-bit integer (2 registers)

## Data Format Conversion

### Float (IEEE 754)
Floating-point numbers are stored in two consecutive 16-bit registers using IEEE 754 format with big-endian byte order (Modbus standard).

### 32-bit Integer
32-bit integers are stored in two consecutive 16-bit registers:
- First register: High word (bits 31-16)
- Second register: Low word (bits 15-0)

## Error Handling

The application includes comprehensive error handling for:
- Connection failures
- Communication timeouts
- Invalid register addresses
- Data conversion errors
- Device not responding

## Testing

To test the application, you can:

1. **Use a Modbus Simulator**: Install a Modbus TCP/RTU simulator
2. **Use ModbusPoll/ModbusSlave**: Popular Modbus testing tools
3. **Use Physical Devices**: Connect to actual Modbus devices

### Popular Modbus Simulators
- ModbusPal (Free, Java-based)
- Modbus Slave (Witte Software)
- QModMaster (Free, Qt-based)

## Troubleshooting

### Common Issues

1. **TCP Connection Failed**
   - Verify the server IP and port
   - Ensure the Modbus TCP server is running
   - Check firewall settings

2. **RTU Connection Failed**
   - Verify the serial port name (COM1, COM2, etc.)
   - Check baud rate and other serial parameters
   - Ensure the device is connected and powered

3. **Device Not Responding**
   - Verify the slave ID is correct
   - Check cable connections
   - Ensure the device supports the requested register addresses

4. **Permission Denied (Serial Port)**
   - Run the application as administrator
   - Ensure no other application is using the serial port

## License

This is a demonstration application for educational purposes.
