using System.Text;
using System.Linq;


namespace ModBusCom
{
    /// <summary>
    /// Structure for serial number data containing product information and test results
    /// </summary>
    public struct SerialNumberData
    {
        /// <summary>
        /// 20-character serial number (padded with spaces if shorter)
        /// </summary>
        public string SerialNumber { get; set; }

        /// <summary>
        /// Single character grade (A, B, C, etc.)
        /// </summary>
        public char Grade { get; set; }

        /// <summary>
        /// Single character status: '1' for Accepted, '2' for Rejected
        /// </summary>
        public char Status { get; set; }

        /// <summary>
        /// 2-byte reason code (0-65535)
        /// </summary>
        public ushort ReasonCode { get; set; }

        /// <summary>
        /// Total size in bytes: 20 (serial) + 1 (grade) + 1 (status) + 2 (reason) = 24 bytes
        /// </summary>
        public const int TotalSizeBytes = 24;

        /// <summary>
        /// Constructor to create a new SerialNumberData instance
        /// </summary>
        /// <param name="serialNumber">Serial number (will be truncated or padded to 20 chars)</param>
        /// <param name="grade">Grade character</param>
        /// <param name="status">Status character ('1' or '2')</param>
        /// <param name="reasonCode">Reason code (0-65535)</param>
        public SerialNumberData(string serialNumber, char grade, char status, ushort reasonCode)
        {
            // Ensure serial number is exactly 20 characters
            SerialNumber = serialNumber?.PadRight(20).Substring(0, 20) ?? new string('\0', 20);
            Grade = grade;
            Status = status;
            ReasonCode = reasonCode;
        }

        /// <summary>
        /// Convert the structure to a byte array for Modbus transmission
        /// </summary>
        /// <returns>24-byte array representing the structure</returns>
        public byte[] ToByteArray()
        {
            byte[] result = new byte[TotalSizeBytes];

            // Serial number (20 bytes)
            byte[] serialBytes = Encoding.ASCII.GetBytes(SerialNumber.PadRight(20).Substring(0, 20));
            Array.Copy(serialBytes, 0, result, 0, 20);

            // Grade (1 byte)
            result[20] = (byte)Grade;

            // Status (1 byte)
            result[21] = (byte)Status;

            // Reason code (2 bytes, big-endian)
            result[22] = (byte)(ReasonCode >> 8);
            result[23] = (byte)(ReasonCode & 0xFF);

            return result;
        }

        /// <summary>
        /// Create a SerialNumberData instance from a byte array
        /// </summary>
        /// <param name="data">24-byte array containing the structure data</param>
        /// <returns>SerialNumberData instance</returns>
        /// <exception cref="ArgumentException">Thrown if data array is not exactly 24 bytes</exception>
        public static SerialNumberData FromByteArray(byte[] data)
        {
            if (data.Length != TotalSizeBytes)
                throw new ArgumentException($"Data array must be exactly {TotalSizeBytes} bytes long");

            // Extract serial number (20 bytes)
            string serialNumber = Encoding.ASCII.GetString(data, 0, 20).TrimEnd();

            // Extract grade (1 byte)
            char grade = (char)data[20];

            // Extract status (1 byte)
            char status = (char)data[21];

            // Extract reason code (2 bytes, big-endian)
            ushort reasonCode = (ushort)((data[22] << 8) | data[23]);

            return new SerialNumberData(serialNumber, grade, status, reasonCode);
        }

        /// <summary>
        /// Convert the structure to Modbus registers (12 registers for 24 bytes)
        /// </summary>
        /// <returns>Array of 12 ushort values representing the structure</returns>
        public ushort[] ToModbusRegisters()
        {
            byte[] bytes = ToByteArray();
            ushort[] registers = new ushort[12]; // 24 bytes = 12 registers

            for (int i = 0; i < 12; i++)
            {
                registers[i] = (ushort)((bytes[i * 2] << 8) | bytes[i * 2 + 1]);
            }

            return registers;
        }

        /// <summary>
        /// Create a SerialNumberData instance from Modbus registers
        /// </summary>
        /// <param name="registers">Array of 12 ushort values</param>
        /// <returns>SerialNumberData instance</returns>
        /// <exception cref="ArgumentException">Thrown if registers array is not exactly 12 elements</exception>
        public static SerialNumberData FromModbusRegisters(ushort[] registers)
        {
            if (registers.Length != 12)
                throw new ArgumentException("Registers array must contain exactly 12 elements");

            byte[] bytes = new byte[24];
            for (int i = 0; i < 12; i++)
            {
                bytes[i * 2] = (byte)(registers[i] >> 8);
                bytes[i * 2 + 1] = (byte)(registers[i] & 0xFF);
            }

            return FromByteArray(bytes);
        }

        /// <summary>
        /// Check if the status indicates acceptance
        /// </summary>
        public bool IsAccepted => Status == '1';

        /// <summary>
        /// Check if the status indicates rejection
        /// </summary>
        public bool IsRejected => Status == '2';

        /// <summary>
        /// String representation of the structure
        /// </summary>
        public override string ToString()
        {
            return $"SN: {SerialNumber.Trim()}, Grade: {Grade}, Status: {Status} ({(IsAccepted ? "Accepted" : IsRejected ? "Rejected" : "Unknown")}), Reason: {ReasonCode}";
        }

        /// <summary>
        /// Generate a random SerialNumberData for demo purposes
        /// </summary>
        /// <param name="random">Random number generator instance</param>
        /// <returns>Random SerialNumberData instance</returns>
        public static SerialNumberData GenerateRandom(Random? random = null)
        {
            random ??= new Random();

            // Generate random serial number (mix of letters and numbers)
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            string serialNumber = new string(Enumerable.Repeat(chars, random.Next(8, 16))
                .Select(s => s[random.Next(s.Length)]).ToArray());

            // Random grade (A-F)
            char[] grades = { 'A', 'B', 'C', 'D', 'E', 'F' };
            char grade = grades[random.Next(grades.Length)];

            // Random status (80% accepted, 20% rejected for realistic demo)
            char status = random.NextDouble() < 0.8 ? '1' : '2';

            // Random reason code (0 for accepted, 1-999 for rejected)
            ushort reasonCode = status == '1' ? (ushort)0 : (ushort)random.Next(1, 1000);

            return new SerialNumberData(serialNumber, grade, status, reasonCode);
        }

        /// <summary>
        /// Generate multiple random SerialNumberData instances
        /// </summary>
        /// <param name="count">Number of instances to generate</param>
        /// <param name="random">Random number generator instance</param>
        /// <returns>Array of random SerialNumberData instances</returns>
        public static SerialNumberData[] GenerateRandomBatch(int count, Random? random = null)
        {
            random ??= new Random();
            SerialNumberData[] batch = new SerialNumberData[count];

            for (int i = 0; i < count; i++)
            {
                batch[i] = GenerateRandom(random);
            }

            return batch;
        }
    }
}