{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"ModBusCom/1.0.0": {"dependencies": {"NModbus": "3.0.81", "NModbus.Serial": "3.0.81", "System.IO.Ports": "9.0.8"}, "runtime": {"ModBusCom.dll": {}}}, "NModbus/3.0.81": {"runtime": {"lib/net6.0/NModbus.dll": {"assemblyVersion": "3.0.81.0", "fileVersion": "3.0.81.0"}}}, "NModbus.Serial/3.0.81": {"dependencies": {"NModbus": "3.0.81", "System.IO.Ports": "9.0.8"}, "runtime": {"lib/net6.0/NModbus.Serial.dll": {"assemblyVersion": "3.0.81.0", "fileVersion": "3.0.81.0"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System.IO.Ports/9.0.8": {"dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.8", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.8", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.8", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.8", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.8", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.8", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.8", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.8", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.8", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.8", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.8", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.8", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.8", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.8", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.8", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.8"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.8": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "System.IO.Ports/9.0.8": {"dependencies": {"runtime.native.System.IO.Ports": "9.0.8"}, "runtime": {"lib/net8.0/System.IO.Ports.dll": {"assemblyVersion": "9.0.0.8", "fileVersion": "9.0.825.36511"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.IO.Ports.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "9.0.0.8", "fileVersion": "9.0.825.36511"}, "runtimes/win/lib/net8.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.8", "fileVersion": "9.0.825.36511"}}}}}, "libraries": {"ModBusCom/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "NModbus/3.0.81": {"type": "package", "serviceable": true, "sha512": "sha512-EzKEp7CHD8ErBL36iMts+6IrZZ9FEqllaD7Y5XzhoRjlxt5yXRughQ1bxPs99QFYFkW5xfkANB0Qs1gAmYGP8Q==", "path": "nmodbus/3.0.81", "hashPath": "nmodbus.3.0.81.nupkg.sha512"}, "NModbus.Serial/3.0.81": {"type": "package", "serviceable": true, "sha512": "sha512-6IISxvysZRaPkUJr52SVLdXqQwtrGtRmOnMbmSsnvjQnTxAUif/NihNC7Zbi4dvzUTDtlg3H7reZIBkUAHupjg==", "path": "nmodbus.serial/3.0.81", "hashPath": "nmodbus.serial.3.0.81.nupkg.sha512"}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-mx4y5oocOjcA/IF41pVyXK4tVEi/FYC9j4F64VnLBd5XfyiBDckxya0hmw/mzcEXqHg+0u6w13cAF1yfKM5XPQ==", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.android-arm.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-5Kupr4pq5NzKdoT9ZtgFYusriJpFRjsEjZ1yOPE7U8e25dzUKwirM5gUwBWSm+rKJNA5axH6TyqtAOgTbzstIg==", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.android-arm64.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-zWUp8Lpw4WH/vT/Z/MGEEIechcTW0GA5ltEXFs/hsX5cLhkd4btTEPytu6BlPOzbbwigmk3+XKaKTUhpwJSNQw==", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.android-x64.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-aM1SZjg5rAs8hRy/WtoMnZqV73/unxDYD9JI1iWjq7KZcl3RKAJgtJlXZ69gxuy3fPWBHWPlQkxOd4GO0OK1xA==", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.android-x86.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-2TZP1rZmIqs3a7SrEk1+wcjICoZlZPazvjYeUWpMwAw9ycjgMb59FdZ+SSiGauPxM9vqsLvUMnk/Gvgs95chog==", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-leXVm3sCtL+FyYuYMJoVfjlH/RBCttmh/6Qia+ThO+3OyfiR7Vq6/C3R16UlzYmKYTC/4kApJ0awirJIriHbOw==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-uxiSRfEtZ4wOHd4lDXviq/GAxfjXVfd10nRUJBmvlA1K9Q48gs172Y0vASNeBE1KqdCGsnKQiQNZKslGOzgYrw==", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-2lVok1LvTxPhL4X8QDjzf7oZz4GoPIwUqSYc/bD1HlA0G07GSO7cKxg5vyHEiZvaFqs/kCNXcXV9wss8l7Aqug==", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-8ZkLfxHnpyypf7UYEvgoxjgoVRCbEMBLuHuCBdYp5aaVP0RvTKn+txiu+gGij6Ckp5H4v/m0LpxcOmmE9udfiw==", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-aAB724RSbJIXy3lyrU4EzMrl4y05ZbeNBdLwUoTNKrWXN7z5Eo7Fs5hBZ1xLBlAAhyHlS9BVigopFe0bTb5wEA==", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-S9X3w6RbqQ31bbIdOHEuTN3swXkwUSiHAWiQIO8DDuzgjqx438DJZXsuWdMYCbWZqBGo2nBQeL0PirntmxZVqA==", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-cYTJETykU/+m9VmuaHm9DLjQMtwwLqLJwuFHbrNZfjR+sW5lV54QGIkePOUYR9Ge3eifZSz7sB3JguwxclwJqw==", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-p0AOD65TJKW6hiRnEQrjXnfNwh+Z+6AveK66ytUi1YHfsNwA3m2D5wxCb/+laceJcW3tWmGQC4wVpxxWiB2VmQ==", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-hOXOxQlvfYJeSPBVbAFcjV3y6FxbaM6EbM1kwklYYqhi/JUQhF0rHUpSfVaYonSeZMARPTOYldD9zz0BbaAmSg==", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-FGSpOMEGXdjHXA2rukVDOmbsqRymmkDIyFw8Lzgj5zlMf7amNMLiAn9XisLpC37AGZlUVZ6ajk53YaDdoE9r+g==", "path": "runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-/i5sHA/x7DRF5E0s7DZ66ZVYKn6P1mZhuz6YWMZ1X8XvGyB98HYc08A3/Y+4YLNBWD5LsJilU4Wlg3CHV6m0XQ==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-v6PkdXUCie8vzrEN8E7Q+WTI5EBfbYPXnzS90GHsrcZYMewhYzV2acVR94b5xeZc0vnTyYr/IfkCf+ktyoLh+w==", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.8", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.9.0.8.nupkg.sha512"}, "System.IO.Ports/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-ZAy9ZcjVBLyt9VTOhChKTgru4jpwF5k1yg1glWnSInKW1ylkhef/WJ00vfLW0HzQfRVZi9NtPBZkv7RZS3zS/g==", "path": "system.io.ports/9.0.8", "hashPath": "system.io.ports.9.0.8.nupkg.sha512"}}}