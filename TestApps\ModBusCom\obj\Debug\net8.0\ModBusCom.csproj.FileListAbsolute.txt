C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\ModBusCom.exe
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\ModBusCom.deps.json
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\ModBusCom.runtimeconfig.json
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\ModBusCom.dll
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\ModBusCom.pdb
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\NModbus.dll
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\NModbus.Serial.dll
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\System.IO.Ports.dll
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\android-arm\native\libSystem.IO.Ports.Native.so
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\android-arm64\native\libSystem.IO.Ports.Native.so
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\android-x64\native\libSystem.IO.Ports.Native.so
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\android-x86\native\libSystem.IO.Ports.Native.so
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\linux-arm\native\libSystem.IO.Ports.Native.so
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\linux-arm64\native\libSystem.IO.Ports.Native.so
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\linux-bionic-arm64\native\libSystem.IO.Ports.Native.so
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\linux-bionic-x64\native\libSystem.IO.Ports.Native.so
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\linux-musl-arm\native\libSystem.IO.Ports.Native.so
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\linux-musl-arm64\native\libSystem.IO.Ports.Native.so
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\linux-musl-x64\native\libSystem.IO.Ports.Native.so
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\linux-x64\native\libSystem.IO.Ports.Native.so
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libSystem.IO.Ports.Native.dylib
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libSystem.IO.Ports.Native.dylib
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\osx-arm64\native\libSystem.IO.Ports.Native.dylib
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\osx-x64\native\libSystem.IO.Ports.Native.dylib
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\unix\lib\net8.0\System.IO.Ports.dll
C:\Sources\TestApps\ModBusCom\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.IO.Ports.dll
C:\Sources\TestApps\ModBusCom\obj\Debug\net8.0\ModBusCom.csproj.AssemblyReference.cache
C:\Sources\TestApps\ModBusCom\obj\Debug\net8.0\ModBusCom.GeneratedMSBuildEditorConfig.editorconfig
C:\Sources\TestApps\ModBusCom\obj\Debug\net8.0\ModBusCom.AssemblyInfoInputs.cache
C:\Sources\TestApps\ModBusCom\obj\Debug\net8.0\ModBusCom.AssemblyInfo.cs
C:\Sources\TestApps\ModBusCom\obj\Debug\net8.0\ModBusCom.csproj.CoreCompileInputs.cache
C:\Sources\TestApps\ModBusCom\obj\Debug\net8.0\ModBusCom.csproj.Up2Date
C:\Sources\TestApps\ModBusCom\obj\Debug\net8.0\ModBusCom.dll
C:\Sources\TestApps\ModBusCom\obj\Debug\net8.0\refint\ModBusCom.dll
C:\Sources\TestApps\ModBusCom\obj\Debug\net8.0\ModBusCom.pdb
C:\Sources\TestApps\ModBusCom\obj\Debug\net8.0\ModBusCom.genruntimeconfig.cache
C:\Sources\TestApps\ModBusCom\obj\Debug\net8.0\ref\ModBusCom.dll
