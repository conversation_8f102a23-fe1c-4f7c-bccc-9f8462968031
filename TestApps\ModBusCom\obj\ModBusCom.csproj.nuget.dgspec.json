{"format": 1, "restore": {"C:\\Sources\\TestApps\\ModBusCom\\ModBusCom.csproj": {}}, "projects": {"C:\\Sources\\TestApps\\ModBusCom\\ModBusCom.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Sources\\TestApps\\ModBusCom\\ModBusCom.csproj", "projectName": "ModBusCom", "projectPath": "C:\\Sources\\TestApps\\ModBusCom\\ModBusCom.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Sources\\TestApps\\ModBusCom\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"NModbus": {"target": "Package", "version": "[3.0.81, )"}, "NModbus.Serial": {"target": "Package", "version": "[3.0.81, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413/PortableRuntimeIdentifierGraph.json"}}}}}