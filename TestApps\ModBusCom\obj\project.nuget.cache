{"version": 2, "dgSpecHash": "83N9p3tfo3k=", "success": true, "projectFilePath": "C:\\Sources\\TestApps\\ModBusCom\\ModBusCom.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\nmodbus\\3.0.81\\nmodbus.3.0.81.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nmodbus.serial\\3.0.81\\nmodbus.serial.3.0.81.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-arm.runtime.native.system.io.ports\\9.0.8\\runtime.android-arm.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-arm64.runtime.native.system.io.ports\\9.0.8\\runtime.android-arm64.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-x64.runtime.native.system.io.ports\\9.0.8\\runtime.android-x64.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-x86.runtime.native.system.io.ports\\9.0.8\\runtime.android-x86.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm.runtime.native.system.io.ports\\9.0.8\\runtime.linux-arm.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm64.runtime.native.system.io.ports\\9.0.8\\runtime.linux-arm64.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-bionic-arm64.runtime.native.system.io.ports\\9.0.8\\runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-bionic-x64.runtime.native.system.io.ports\\9.0.8\\runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-musl-arm.runtime.native.system.io.ports\\9.0.8\\runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-musl-arm64.runtime.native.system.io.ports\\9.0.8\\runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-musl-x64.runtime.native.system.io.ports\\9.0.8\\runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-x64.runtime.native.system.io.ports\\9.0.8\\runtime.linux-x64.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.maccatalyst-arm64.runtime.native.system.io.ports\\9.0.8\\runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.maccatalyst-x64.runtime.native.system.io.ports\\9.0.8\\runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.ports\\9.0.8\\runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-arm64.runtime.native.system.io.ports\\9.0.8\\runtime.osx-arm64.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-x64.runtime.native.system.io.ports\\9.0.8\\runtime.osx-x64.runtime.native.system.io.ports.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.ports\\9.0.8\\system.io.ports.9.0.8.nupkg.sha512"], "logs": []}